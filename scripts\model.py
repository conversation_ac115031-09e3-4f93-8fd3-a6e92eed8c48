"""
CNN Model for Hand Gesture Recognition
Implements 1D CNN architecture for landmark-based gesture classification
"""

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TF_AVAILABLE = True
except ImportError:
    print("TensorFlow not available, using sklearn alternative")
    TF_AVAILABLE = False
import numpy as np
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

class GestureCNN:
    def __init__(self, num_classes=6, input_shape=(63, 1)):
        self.num_classes = num_classes
        self.input_shape = input_shape
        self.model = None
        self.history = None
        
    def build_model(self):
        """
        Build 1D CNN model architecture as specified
        """
        model = keras.Sequential([
            # Input layer
            layers.Input(shape=self.input_shape),
            
            # First Conv1D block
            layers.Conv1D(filters=64, kernel_size=3, activation='relu', padding='same'),
            layers.MaxPooling1D(pool_size=2),
            
            # Second Conv1D block  
            layers.Conv1D(filters=128, kernel_size=3, activation='relu', padding='same'),
            layers.MaxPooling1D(pool_size=2),
            
            # Third Conv1D block
            layers.Conv1D(filters=256, kernel_size=3, activation='relu', padding='same'),
            layers.MaxPooling1D(pool_size=2),
            
            # Flatten for dense layers
            layers.Flatten(),
            
            # Dropout for regularization
            layers.Dropout(0.3),
            
            # Dense layers
            layers.Dense(512, activation='relu'),
            layers.Dense(256, activation='relu'),
            
            # Output layer
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        self.model = model
        return model
    
    def compile_model(self, learning_rate=0.001):
        """
        Compile the model with specified optimizer and loss
        """
        if self.model is None:
            self.build_model()
            
        self.model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=learning_rate),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return self.model
    
    def get_model_summary(self):
        """
        Print model architecture summary
        """
        if self.model is None:
            self.build_model()
            
        return self.model.summary()
    
    def prepare_callbacks(self, model_save_path="models/gesture_cnn.h5"):
        """
        Prepare training callbacks
        """
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=5,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=3,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        return callbacks
    
    def train(self, X_train, y_train, X_val, y_val, 
              epochs=10, batch_size=32, model_save_path="models/gesture_cnn.h5"):
        """
        Train the CNN model
        """
        if self.model is None:
            self.compile_model()
            
        # Prepare callbacks
        callbacks = self.prepare_callbacks(model_save_path)
        
        # Train the model
        print("Starting training...")
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
        return self.history
    
    def evaluate(self, X_test, y_test):
        """
        Evaluate model performance
        """
        if self.model is None:
            raise ValueError("Model not trained yet!")
            
        # Evaluate on test set
        test_loss, test_accuracy = self.model.evaluate(X_test, y_test, verbose=0)
        
        # Get predictions
        y_pred = self.model.predict(X_test)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(y_test, axis=1)
        
        return {
            'test_loss': test_loss,
            'test_accuracy': test_accuracy,
            'y_pred': y_pred,
            'y_pred_classes': y_pred_classes,
            'y_true_classes': y_true_classes
        }
    
    def plot_training_history(self, save_path="results/training_history.png"):
        """
        Plot training and validation accuracy/loss curves
        """
        if self.history is None:
            print("No training history available!")
            return
            
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Training history plot saved to {save_path}")
    
    def plot_confusion_matrix(self, y_true, y_pred, class_names, 
                            save_path="results/confusion_matrix.png"):
        """
        Plot confusion matrix
        """
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Confusion matrix saved to {save_path}")
    
    def save_model(self, filepath="models/gesture_cnn.h5"):
        """
        Save the trained model
        """
        if self.model is None:
            raise ValueError("No model to save!")
            
        self.model.save(filepath)
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath="models/gesture_cnn.h5"):
        """
        Load a pre-trained model
        """
        self.model = keras.models.load_model(filepath)
        print(f"Model loaded from {filepath}")
        return self.model
    
    def predict(self, X):
        """
        Make predictions on new data
        """
        if self.model is None:
            raise ValueError("Model not loaded!")
            
        predictions = self.model.predict(X)
        return predictions
    
    def predict_single(self, landmarks):
        """
        Predict gesture for a single landmark vector
        """
        if self.model is None:
            raise ValueError("Model not loaded!")
            
        # Reshape for model input
        landmarks = landmarks.reshape(1, 63, 1)
        
        # Get prediction
        prediction = self.model.predict(landmarks, verbose=0)
        predicted_class = np.argmax(prediction)
        confidence = np.max(prediction)
        
        return predicted_class, confidence

def create_model_architecture_diagram():
    """
    Create a text-based diagram of the model architecture
    """
    architecture = """
    Hand Gesture CNN Architecture
    =============================
    
    Input: (63, 1) - Hand landmark features
           ↓
    Conv1D(64, kernel=3) + ReLU + MaxPool
           ↓
    Conv1D(128, kernel=3) + ReLU + MaxPool  
           ↓
    Conv1D(256, kernel=3) + ReLU + MaxPool
           ↓
    Flatten
           ↓
    Dropout(0.3)
           ↓
    Dense(512) + ReLU
           ↓
    Dense(256) + ReLU
           ↓
    Dense(6) + Softmax
           ↓
    Output: 6 gesture classes
    """
    
    return architecture

if __name__ == "__main__":
    # Create and display model
    model = GestureCNN()
    model.build_model()
    print(create_model_architecture_diagram())
    model.get_model_summary()
