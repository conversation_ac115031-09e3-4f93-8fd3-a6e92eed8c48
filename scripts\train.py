"""
Training Script for Hand Gesture Recognition CNN
Trains the model on processed landmark data
"""

import numpy as np
import pickle
import os
from pathlib import Path
from sklearn.preprocessing import LabelEncoder
from tensorflow.keras.utils import to_categorical
import tensorflow as tf

# Import our custom model
from model import GestureCNN

class GestureTrainer:
    def __init__(self, data_path="dataset/processed", model_path="models"):
        self.data_path = Path(data_path)
        self.model_path = Path(model_path)
        self.model_path.mkdir(exist_ok=True)
        
        self.gesture_classes = [
            "open_palm", "pinch", "thumb_middle_pinch", 
            "two_fingers_up", "two_fingers_down", "closed_fist"
        ]
        
    def load_data(self):
        """
        Load processed landmark data
        """
        print("Loading processed data...")
        
        data = {}
        
        # Load each split
        for split in ['train', 'val', 'test']:
            X_file = self.data_path / f'X_{split}.npy'
            y_file = self.data_path / f'y_{split}.pkl'
            
            if X_file.exists() and y_file.exists():
                X = np.load(X_file)
                with open(y_file, 'rb') as f:
                    y = pickle.load(f)
                    
                print(f"Loaded {split}: {X.shape[0]} samples")
                data[split] = (X, y)
            else:
                print(f"Warning: {split} data not found!")
                
        # Load label encoder
        le_file = self.data_path / 'label_encoder.pkl'
        if le_file.exists():
            with open(le_file, 'rb') as f:
                label_encoder = pickle.load(f)
            data['label_encoder'] = label_encoder
            print(f"Label encoder loaded. Classes: {label_encoder.classes_}")
        else:
            print("Warning: Label encoder not found!")
            
        return data
    
    def prepare_data_for_training(self, data):
        """
        Prepare data for CNN training
        """
        print("Preparing data for training...")
        
        label_encoder = data['label_encoder']
        
        prepared_data = {}
        
        for split in ['train', 'val', 'test']:
            if split not in data:
                continue
                
            X, y = data[split]
            
            # Reshape X for 1D CNN: (samples, features, channels)
            X_reshaped = X.reshape(X.shape[0], X.shape[1], 1)
            
            # Encode labels
            y_encoded = label_encoder.transform(y)
            
            # Convert to categorical (one-hot encoding)
            y_categorical = to_categorical(y_encoded, num_classes=len(self.gesture_classes))
            
            prepared_data[split] = (X_reshaped, y_categorical)
            
            print(f"{split} data shape: X={X_reshaped.shape}, y={y_categorical.shape}")
            
        return prepared_data
    
    def train_model(self, prepared_data, epochs=10, batch_size=32):
        """
        Train the CNN model
        """
        print("Initializing CNN model...")
        
        # Initialize model
        model = GestureCNN(num_classes=len(self.gesture_classes))
        model.compile_model()
        
        # Display model architecture
        print("\nModel Architecture:")
        model.get_model_summary()
        
        # Get training data
        X_train, y_train = prepared_data['train']
        X_val, y_val = prepared_data['val']
        
        # Train the model
        model_save_path = self.model_path / "gesture_cnn.h5"
        
        history = model.train(
            X_train, y_train,
            X_val, y_val,
            epochs=epochs,
            batch_size=batch_size,
            model_save_path=str(model_save_path)
        )
        
        # Plot training history
        model.plot_training_history(save_path="results/training_history.png")
        
        return model, history
    
    def create_synthetic_data(self):
        """
        Create synthetic landmark data for demonstration
        This is used when no real processed data is available
        """
        print("Creating synthetic landmark data for demonstration...")
        
        np.random.seed(42)
        
        # Number of samples per class per split
        samples_per_class = {'train': 100, 'val': 20, 'test': 20}
        
        data = {}
        all_labels = []
        
        for split in ['train', 'val', 'test']:
            X_split = []
            y_split = []
            
            for i, gesture_class in enumerate(self.gesture_classes):
                n_samples = samples_per_class[split]
                
                # Generate synthetic landmark data
                # Each gesture has slightly different patterns
                base_landmarks = np.random.normal(0, 0.1, (n_samples, 63))
                
                # Add gesture-specific patterns
                if gesture_class == "open_palm":
                    base_landmarks += np.random.normal(0.2, 0.05, (n_samples, 63))
                elif gesture_class == "closed_fist":
                    base_landmarks += np.random.normal(-0.2, 0.05, (n_samples, 63))
                elif gesture_class == "pinch":
                    base_landmarks += np.random.normal(0.1, 0.03, (n_samples, 63))
                elif gesture_class == "thumb_middle_pinch":
                    base_landmarks += np.random.normal(0.15, 0.03, (n_samples, 63))
                elif gesture_class == "two_fingers_up":
                    base_landmarks += np.random.normal(0.3, 0.04, (n_samples, 63))
                elif gesture_class == "two_fingers_down":
                    base_landmarks += np.random.normal(-0.3, 0.04, (n_samples, 63))
                
                X_split.append(base_landmarks)
                y_split.extend([gesture_class] * n_samples)
                all_labels.extend([gesture_class] * n_samples)
            
            X_split = np.vstack(X_split)
            data[split] = (X_split, y_split)
            
            print(f"Created {split} data: {X_split.shape[0]} samples")
        
        # Create label encoder
        label_encoder = LabelEncoder()
        label_encoder.fit(all_labels)
        data['label_encoder'] = label_encoder
        
        # Save synthetic data
        self.data_path.mkdir(exist_ok=True)
        
        for split in ['train', 'val', 'test']:
            X, y = data[split]
            np.save(self.data_path / f'X_{split}.npy', X)
            with open(self.data_path / f'y_{split}.pkl', 'wb') as f:
                pickle.dump(y, f)
        
        with open(self.data_path / 'label_encoder.pkl', 'wb') as f:
            pickle.dump(label_encoder, f)
            
        print("Synthetic data saved to dataset/processed/")
        
        return data
    
    def run_training_pipeline(self, epochs=10, batch_size=32, use_synthetic=True):
        """
        Run the complete training pipeline
        """
        print("=== Hand Gesture Recognition Training Pipeline ===")
        
        # Load or create data
        if use_synthetic or not (self.data_path / 'X_train.npy').exists():
            print("Using synthetic data for demonstration...")
            data = self.create_synthetic_data()
        else:
            data = self.load_data()
        
        if not data:
            print("No data available for training!")
            return None
            
        # Prepare data for training
        prepared_data = self.prepare_data_for_training(data)
        
        # Train model
        model, history = self.train_model(prepared_data, epochs, batch_size)
        
        print("\n=== Training Completed Successfully! ===")
        print(f"Model saved to: {self.model_path / 'gesture_cnn.h5'}")
        print(f"Training history plot saved to: results/training_history.png")
        
        return model, history, prepared_data

def main():
    """
    Main training function
    """
    # Set random seeds for reproducibility
    np.random.seed(42)
    tf.random.set_seed(42)
    
    # Create results directory
    Path("results").mkdir(exist_ok=True)
    
    # Initialize trainer
    trainer = GestureTrainer()
    
    # Run training pipeline
    result = trainer.run_training_pipeline(
        epochs=10,
        batch_size=32,
        use_synthetic=True  # Set to False if you have real processed data
    )
    
    if result:
        model, history, prepared_data = result
        print("\nTraining pipeline completed successfully!")
        print("Next steps:")
        print("1. Run test.py to evaluate the model")
        print("2. Run realtime.py for live gesture recognition")
    else:
        print("Training failed!")

if __name__ == "__main__":
    main()
