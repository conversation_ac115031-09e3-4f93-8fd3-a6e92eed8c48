"""
Setup and Installation Script for Hand Gesture Recognition System
Automates the complete setup process
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class GestureSystemSetup:
    def __init__(self):
        self.project_root = Path.cwd()
        self.python_executable = sys.executable
        
    def check_python_version(self):
        """Check if Python version is compatible"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8 or higher is required!")
            print(f"Current version: {version.major}.{version.minor}.{version.micro}")
            return False
        
        print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def install_dependencies(self):
        """Install required dependencies"""
        print("\n📦 Installing dependencies...")
        
        try:
            # Upgrade pip first
            subprocess.check_call([
                self.python_executable, "-m", "pip", "install", "--upgrade", "pip"
            ])
            
            # Install requirements
            requirements_file = self.project_root / "requirements.txt"
            if requirements_file.exists():
                subprocess.check_call([
                    self.python_executable, "-m", "pip", "install", "-r", str(requirements_file)
                ])
                print("✅ Dependencies installed successfully!")
                return True
            else:
                print("❌ requirements.txt not found!")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing dependencies: {e}")
            return False
    
    def check_camera_access(self):
        """Check if camera is accessible"""
        print("\n📷 Checking camera access...")
        
        try:
            import cv2
            cap = cv2.VideoCapture(0)
            
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                
                if ret:
                    print("✅ Camera access successful!")
                    return True
                else:
                    print("❌ Camera detected but cannot read frames!")
                    return False
            else:
                print("❌ Cannot access camera!")
                print("Please check:")
                print("- Camera is connected")
                print("- Camera permissions are granted")
                print("- No other application is using the camera")
                return False
                
        except ImportError:
            print("❌ OpenCV not installed!")
            return False
        except Exception as e:
            print(f"❌ Camera check failed: {e}")
            return False
    
    def create_directories(self):
        """Create necessary directories"""
        print("\n📁 Creating project directories...")
        
        directories = [
            "dataset/raw",
            "dataset/processed", 
            "dataset/train",
            "dataset/val",
            "dataset/test",
            "models",
            "results"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            
        print("✅ Directories created successfully!")
        return True
    
    def test_imports(self):
        """Test if all required modules can be imported"""
        print("\n🔍 Testing module imports...")
        
        required_modules = [
            "tensorflow",
            "keras", 
            "cv2",
            "mediapipe",
            "numpy",
            "matplotlib",
            "sklearn",
            "pyautogui",
            "pandas",
            "seaborn"
        ]
        
        failed_imports = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                print(f"❌ {module}")
                failed_imports.append(module)
        
        if failed_imports:
            print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
            return False
        else:
            print("\n✅ All modules imported successfully!")
            return True
    
    def run_quick_test(self):
        """Run a quick functionality test"""
        print("\n🧪 Running quick functionality test...")
        
        try:
            # Test MediaPipe
            import mediapipe as mp
            mp_hands = mp.solutions.hands
            hands = mp_hands.Hands(static_image_mode=True)
            print("✅ MediaPipe hands initialized")
            
            # Test TensorFlow
            import tensorflow as tf
            print(f"✅ TensorFlow version: {tf.__version__}")
            
            # Test model creation
            sys.path.append(str(self.project_root / "scripts"))
            from model import GestureCNN
            
            model = GestureCNN()
            model.build_model()
            print("✅ CNN model creation successful")
            
            return True
            
        except Exception as e:
            print(f"❌ Quick test failed: {e}")
            return False
    
    def display_next_steps(self):
        """Display next steps for the user"""
        print("\n" + "="*50)
        print("🎉 SETUP COMPLETED SUCCESSFULLY!")
        print("="*50)
        
        print("\n📋 Next Steps:")
        print("1. Prepare dataset:")
        print("   python scripts/dataset_preparation.py")
        
        print("\n2. Extract landmarks:")
        print("   python scripts/preprocess.py")
        
        print("\n3. Train the model:")
        print("   python scripts/train.py")
        
        print("\n4. Evaluate performance:")
        print("   python scripts/test.py")
        
        print("\n5. Start real-time recognition:")
        print("   python scripts/realtime.py")
        
        print("\n💡 Tips:")
        print("- Ensure good lighting for better recognition")
        print("- Keep hand 0.5-2.5 meters from camera")
        print("- Press 'q' to quit real-time recognition")
        print("- Press 'm' to toggle mouse control")
        
        print("\n📖 For detailed instructions, see README.md")
    
    def run_setup(self):
        """Run the complete setup process"""
        print("🚀 Hand Gesture Recognition System Setup")
        print("="*45)
        
        # Check Python version
        if not self.check_python_version():
            return False
        
        # Create directories
        if not self.create_directories():
            return False
        
        # Install dependencies
        if not self.install_dependencies():
            return False
        
        # Test imports
        if not self.test_imports():
            return False
        
        # Check camera access
        camera_ok = self.check_camera_access()
        if not camera_ok:
            print("\n⚠️  Camera access failed, but setup will continue.")
            print("Real-time recognition may not work without camera access.")
        
        # Run quick test
        if not self.run_quick_test():
            return False
        
        # Display next steps
        self.display_next_steps()
        
        return True

def main():
    """Main setup function"""
    setup = GestureSystemSetup()
    
    try:
        success = setup.run_setup()
        
        if success:
            print("\n✅ Setup completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Setup failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
