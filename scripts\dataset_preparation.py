"""
Hand Gesture Dataset Preparation Script
Downloads and organizes hand gesture dataset for training
"""

import os
import cv2
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
import shutil
import urllib.request
import zipfile
from pathlib import Path

class DatasetPreparator:
    def __init__(self, base_path="dataset"):
        self.base_path = Path(base_path)
        self.raw_path = self.base_path / "raw"
        self.processed_path = self.base_path / "processed"
        self.train_path = self.base_path / "train"
        self.val_path = self.base_path / "val"
        self.test_path = self.base_path / "test"
        
        # Gesture classes
        self.gesture_classes = [
            "open_palm",
            "pinch",
            "thumb_middle_pinch", 
            "two_fingers_up",
            "two_fingers_down",
            "closed_fist"
        ]
        
    def create_sample_dataset(self):
        """
        Creates a sample dataset structure for demonstration
        In a real scenario, you would download from Kaggle or other sources
        """
        print("Creating sample dataset structure...")
        
        # Create class directories
        for split in ['train', 'val', 'test']:
            split_path = self.base_path / split
            split_path.mkdir(exist_ok=True)
            
            for gesture in self.gesture_classes:
                gesture_path = split_path / gesture
                gesture_path.mkdir(exist_ok=True)
                
        print("Sample dataset structure created!")
        print("Note: In a real implementation, download actual gesture images from:")
        print("- Kaggle: Hand Gesture Recognition Database")
        print("- GitHub: Hand Gesture datasets")
        print("- Custom collection using webcam")
        
    def download_sample_images(self):
        """
        Creates placeholder images for each gesture class
        Replace this with actual dataset download logic
        """
        print("Creating placeholder images...")
        
        # Create sample images for each class and split
        for split in ['train', 'val', 'test']:
            num_samples = {'train': 100, 'val': 20, 'test': 20}[split]
            
            for gesture in self.gesture_classes:
                gesture_path = self.base_path / split / gesture
                
                for i in range(num_samples):
                    # Create a simple colored image as placeholder
                    img = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
                    
                    # Add some gesture-specific patterns
                    if gesture == "open_palm":
                        cv2.rectangle(img, (50, 50), (174, 174), (255, 255, 255), -1)
                    elif gesture == "closed_fist":
                        cv2.circle(img, (112, 112), 50, (0, 0, 255), -1)
                    elif gesture == "pinch":
                        cv2.circle(img, (100, 100), 20, (255, 0, 0), -1)
                        cv2.circle(img, (124, 100), 20, (255, 0, 0), -1)
                    
                    filename = f"{gesture}_{i:03d}.jpg"
                    cv2.imwrite(str(gesture_path / filename), img)
                    
        print(f"Created placeholder images for {len(self.gesture_classes)} gesture classes")
        
    def apply_data_augmentation(self, image):
        """
        Apply data augmentation techniques to increase dataset variety
        """
        augmented_images = []
        
        # Original image
        augmented_images.append(image)
        
        # Rotation
        for angle in [-15, 15]:
            rows, cols = image.shape[:2]
            M = cv2.getRotationMatrix2D((cols/2, rows/2), angle, 1)
            rotated = cv2.warpAffine(image, M, (cols, rows))
            augmented_images.append(rotated)
            
        # Brightness adjustment
        for beta in [-30, 30]:
            bright = cv2.convertScaleAbs(image, alpha=1.0, beta=beta)
            augmented_images.append(bright)
            
        # Horizontal flip
        flipped = cv2.flip(image, 1)
        augmented_images.append(flipped)
        
        # Scaling
        for scale in [0.9, 1.1]:
            rows, cols = image.shape[:2]
            M = cv2.getRotationMatrix2D((cols/2, rows/2), 0, scale)
            scaled = cv2.warpAffine(image, M, (cols, rows))
            augmented_images.append(scaled)
            
        return augmented_images
    
    def get_dataset_info(self):
        """
        Print dataset information
        """
        print("\n=== Dataset Information ===")
        print(f"Gesture Classes: {len(self.gesture_classes)}")
        for i, gesture in enumerate(self.gesture_classes):
            print(f"{i}: {gesture}")
            
        print(f"\nDataset Structure:")
        for split in ['train', 'val', 'test']:
            split_path = self.base_path / split
            if split_path.exists():
                total_images = sum(len(list((split_path / gesture).glob('*.jpg'))) 
                                 for gesture in self.gesture_classes 
                                 if (split_path / gesture).exists())
                print(f"{split}: {total_images} images")
                
    def prepare_dataset(self):
        """
        Main function to prepare the complete dataset
        """
        print("Starting dataset preparation...")
        
        # Create directory structure
        self.create_sample_dataset()
        
        # Download/create sample images
        self.download_sample_images()
        
        # Display dataset info
        self.get_dataset_info()
        
        print("\nDataset preparation completed!")
        print("Next steps:")
        print("1. Replace placeholder images with real gesture images")
        print("2. Run preprocessing to extract MediaPipe landmarks")
        print("3. Train the CNN model")

if __name__ == "__main__":
    preparator = DatasetPreparator()
    preparator.prepare_dataset()
