"""
Testing and Evaluation Script for Hand Gesture Recognition
Evaluates trained model performance and generates detailed metrics
"""

import numpy as np
import pickle
import os
from pathlib import Path
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_recall_fscore_support
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# Import our custom model
from model import GestureCNN

class GestureEvaluator:
    def __init__(self, model_path="models/gesture_cnn.h5", data_path="dataset/processed"):
        self.model_path = Path(model_path)
        self.data_path = Path(data_path)
        self.results_path = Path("results")
        self.results_path.mkdir(exist_ok=True)
        
        self.gesture_classes = [
            "open_palm", "pinch", "thumb_middle_pinch", 
            "two_fingers_up", "two_fingers_down", "closed_fist"
        ]
        
    def load_test_data(self):
        """
        Load test data for evaluation
        """
        print("Loading test data...")
        
        # Load test features
        X_test_file = self.data_path / 'X_test.npy'
        y_test_file = self.data_path / 'y_test.pkl'
        le_file = self.data_path / 'label_encoder.pkl'
        
        if not all([X_test_file.exists(), y_test_file.exists(), le_file.exists()]):
            print("Test data not found! Please run preprocessing and training first.")
            return None
            
        # Load data
        X_test = np.load(X_test_file)
        with open(y_test_file, 'rb') as f:
            y_test = pickle.load(f)
        with open(le_file, 'rb') as f:
            label_encoder = pickle.load(f)
            
        # Reshape for CNN
        X_test = X_test.reshape(X_test.shape[0], X_test.shape[1], 1)
        
        # Encode labels
        y_test_encoded = label_encoder.transform(y_test)
        
        print(f"Test data loaded: {X_test.shape[0]} samples")
        
        return X_test, y_test, y_test_encoded, label_encoder
    
    def load_model(self):
        """
        Load trained model
        """
        if not self.model_path.exists():
            print(f"Model not found at {self.model_path}")
            print("Please run training first!")
            return None
            
        print(f"Loading model from {self.model_path}")
        model = GestureCNN()
        model.load_model(str(self.model_path))
        
        return model
    
    def evaluate_model(self, model, X_test, y_test_encoded, label_encoder):
        """
        Evaluate model performance
        """
        print("Evaluating model performance...")
        
        # Get predictions
        predictions = model.predict(X_test)
        y_pred = np.argmax(predictions, axis=1)
        
        # Calculate metrics
        accuracy = accuracy_score(y_test_encoded, y_pred)
        precision, recall, f1, support = precision_recall_fscore_support(
            y_test_encoded, y_pred, average=None
        )
        
        # Overall metrics
        precision_avg, recall_avg, f1_avg, _ = precision_recall_fscore_support(
            y_test_encoded, y_pred, average='weighted'
        )
        
        results = {
            'accuracy': accuracy,
            'precision_per_class': precision,
            'recall_per_class': recall,
            'f1_per_class': f1,
            'support_per_class': support,
            'precision_avg': precision_avg,
            'recall_avg': recall_avg,
            'f1_avg': f1_avg,
            'y_true': y_test_encoded,
            'y_pred': y_pred,
            'predictions_proba': predictions
        }
        
        return results
    
    def generate_classification_report(self, results, label_encoder):
        """
        Generate detailed classification report
        """
        print("\n=== Classification Report ===")
        
        # Print overall metrics
        print(f"Overall Accuracy: {results['accuracy']:.4f}")
        print(f"Weighted Precision: {results['precision_avg']:.4f}")
        print(f"Weighted Recall: {results['recall_avg']:.4f}")
        print(f"Weighted F1-Score: {results['f1_avg']:.4f}")
        
        # Per-class metrics
        print(f"\nPer-Class Metrics:")
        print("-" * 80)
        print(f"{'Class':<20} {'Precision':<12} {'Recall':<12} {'F1-Score':<12} {'Support':<10}")
        print("-" * 80)
        
        for i, class_name in enumerate(label_encoder.classes_):
            precision = results['precision_per_class'][i]
            recall = results['recall_per_class'][i]
            f1 = results['f1_per_class'][i]
            support = results['support_per_class'][i]
            
            print(f"{class_name:<20} {precision:<12.4f} {recall:<12.4f} {f1:<12.4f} {support:<10}")
        
        # Save detailed report
        report = classification_report(
            results['y_true'], 
            results['y_pred'], 
            target_names=label_encoder.classes_,
            output_dict=True
        )
        
        # Save to file
        report_file = self.results_path / "classification_report.txt"
        with open(report_file, 'w') as f:
            f.write("Hand Gesture Recognition - Classification Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Overall Accuracy: {results['accuracy']:.4f}\n")
            f.write(f"Weighted Precision: {results['precision_avg']:.4f}\n")
            f.write(f"Weighted Recall: {results['recall_avg']:.4f}\n")
            f.write(f"Weighted F1-Score: {results['f1_avg']:.4f}\n\n")
            
            f.write(classification_report(
                results['y_true'], 
                results['y_pred'], 
                target_names=label_encoder.classes_
            ))
        
        print(f"\nDetailed report saved to: {report_file}")
        
        return report
    
    def plot_confusion_matrix(self, results, label_encoder):
        """
        Plot and save confusion matrix
        """
        cm = confusion_matrix(results['y_true'], results['y_pred'])
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=label_encoder.classes_, 
                   yticklabels=label_encoder.classes_)
        plt.title('Confusion Matrix - Hand Gesture Recognition')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        
        cm_file = self.results_path / "confusion_matrix.png"
        plt.savefig(cm_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Confusion matrix saved to: {cm_file}")
        
        # Calculate per-class accuracy from confusion matrix
        class_accuracies = cm.diagonal() / cm.sum(axis=1)
        
        print("\nPer-class Accuracy from Confusion Matrix:")
        for i, class_name in enumerate(label_encoder.classes_):
            print(f"{class_name}: {class_accuracies[i]:.4f}")
    
    def plot_prediction_confidence(self, results, label_encoder):
        """
        Plot prediction confidence distribution
        """
        predictions_proba = results['predictions_proba']
        max_confidences = np.max(predictions_proba, axis=1)
        
        plt.figure(figsize=(12, 5))
        
        # Overall confidence distribution
        plt.subplot(1, 2, 1)
        plt.hist(max_confidences, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('Prediction Confidence Distribution')
        plt.xlabel('Maximum Confidence')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        
        # Confidence by class
        plt.subplot(1, 2, 2)
        for i, class_name in enumerate(label_encoder.classes_):
            class_mask = results['y_true'] == i
            class_confidences = max_confidences[class_mask]
            plt.hist(class_confidences, bins=10, alpha=0.6, label=class_name)
        
        plt.title('Confidence Distribution by Class')
        plt.xlabel('Maximum Confidence')
        plt.ylabel('Frequency')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        conf_file = self.results_path / "prediction_confidence.png"
        plt.savefig(conf_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Confidence analysis saved to: {conf_file}")
        
        # Print confidence statistics
        print(f"\nConfidence Statistics:")
        print(f"Mean Confidence: {np.mean(max_confidences):.4f}")
        print(f"Std Confidence: {np.std(max_confidences):.4f}")
        print(f"Min Confidence: {np.min(max_confidences):.4f}")
        print(f"Max Confidence: {np.max(max_confidences):.4f}")
    
    def save_metrics_summary(self, results, label_encoder):
        """
        Save comprehensive metrics summary
        """
        summary = {
            'overall_accuracy': results['accuracy'],
            'weighted_precision': results['precision_avg'],
            'weighted_recall': results['recall_avg'],
            'weighted_f1': results['f1_avg'],
            'per_class_metrics': {}
        }
        
        for i, class_name in enumerate(label_encoder.classes_):
            summary['per_class_metrics'][class_name] = {
                'precision': float(results['precision_per_class'][i]),
                'recall': float(results['recall_per_class'][i]),
                'f1_score': float(results['f1_per_class'][i]),
                'support': int(results['support_per_class'][i])
            }
        
        # Save as text file
        metrics_file = self.results_path / "metrics_summary.txt"
        with open(metrics_file, 'w') as f:
            f.write("Hand Gesture Recognition - Metrics Summary\n")
            f.write("=" * 45 + "\n\n")
            f.write(f"Overall Accuracy: {summary['overall_accuracy']:.4f}\n")
            f.write(f"Weighted Precision: {summary['weighted_precision']:.4f}\n")
            f.write(f"Weighted Recall: {summary['weighted_recall']:.4f}\n")
            f.write(f"Weighted F1-Score: {summary['weighted_f1']:.4f}\n\n")
            
            f.write("Per-Class Metrics:\n")
            f.write("-" * 20 + "\n")
            for class_name, metrics in summary['per_class_metrics'].items():
                f.write(f"\n{class_name}:\n")
                f.write(f"  Precision: {metrics['precision']:.4f}\n")
                f.write(f"  Recall: {metrics['recall']:.4f}\n")
                f.write(f"  F1-Score: {metrics['f1_score']:.4f}\n")
                f.write(f"  Support: {metrics['support']}\n")
        
        print(f"Metrics summary saved to: {metrics_file}")
        
        return summary
    
    def run_evaluation(self):
        """
        Run complete evaluation pipeline
        """
        print("=== Hand Gesture Recognition Model Evaluation ===")
        
        # Load test data
        test_data = self.load_test_data()
        if test_data is None:
            return None
            
        X_test, y_test, y_test_encoded, label_encoder = test_data
        
        # Load model
        model = self.load_model()
        if model is None:
            return None
            
        # Evaluate model
        results = self.evaluate_model(model, X_test, y_test_encoded, label_encoder)
        
        # Generate reports and visualizations
        self.generate_classification_report(results, label_encoder)
        self.plot_confusion_matrix(results, label_encoder)
        self.plot_prediction_confidence(results, label_encoder)
        summary = self.save_metrics_summary(results, label_encoder)
        
        print("\n=== Evaluation Completed Successfully! ===")
        print(f"Results saved in: {self.results_path}")
        
        # Check if target accuracy is met
        target_accuracy = 0.90
        if results['accuracy'] >= target_accuracy:
            print(f"✅ Target accuracy of {target_accuracy:.1%} achieved!")
        else:
            print(f"❌ Target accuracy of {target_accuracy:.1%} not met. Current: {results['accuracy']:.1%}")
        
        return results, summary

def main():
    """
    Main evaluation function
    """
    evaluator = GestureEvaluator()
    result = evaluator.run_evaluation()
    
    if result:
        print("\nEvaluation completed successfully!")
        print("Check the 'results/' folder for detailed reports and visualizations.")
    else:
        print("Evaluation failed! Please ensure model and data are available.")

if __name__ == "__main__":
    main()
