# Hand Gesture Recognition System using CNN

A complete real-time hand gesture recognition system that uses Convolutional Neural Networks (CNN) to classify hand gestures and control mouse actions through computer vision.

## 🎯 Features

- **6 Gesture Classes**: Open Palm, Pinch, Thumb+Middle Pinch, Two Fingers Up/Down, Closed Fist
- **Real-time Recognition**: Live webcam feed processing with <100ms latency
- **Mouse Control Integration**: PyAutoGUI integration for gesture-based computer control
- **MediaPipe Landmarks**: 21-point hand landmark extraction for robust feature representation
- **1D CNN Architecture**: Optimized neural network for landmark-based classification
- **Comprehensive Evaluation**: Detailed metrics, confusion matrices, and performance analysis

## 🏗️ Project Structure

```
hand-gesture-using-cnn/
├── dataset/
│   ├── raw/           # Raw gesture images
│   ├── processed/     # Processed landmark data
│   ├── train/         # Training data split
│   ├── val/           # Validation data split
│   └── test/          # Test data split
├── models/
│   └── gesture_cnn.h5 # Trained CNN model
├── scripts/
│   ├── dataset_preparation.py  # Dataset download and organization
│   ├── preprocess.py          # MediaPipe landmark extraction
│   ├── model.py              # CNN architecture definition
│   ├── train.py              # Training pipeline
│   ├── test.py               # Model evaluation
│   └── realtime.py           # Real-time gesture recognition
├── results/
│   ├── training_history.png   # Training curves
│   ├── confusion_matrix.png   # Classification results
│   ├── classification_report.txt
│   └── metrics_summary.txt
├── requirements.txt
└── README.md
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd hand-gesture-using-cnn

# Install dependencies
pip install -r requirements.txt
```

### 2. Dataset Preparation

```bash
# Prepare sample dataset (creates synthetic data for demonstration)
python scripts/dataset_preparation.py

# For real dataset, download from:
# - Kaggle: Hand Gesture Recognition Database
# - Custom collection using webcam
```

### 3. Preprocessing

```bash
# Extract MediaPipe landmarks from images
python scripts/preprocess.py
```

### 4. Training

```bash
# Train the CNN model
python scripts/train.py
```

### 5. Evaluation

```bash
# Evaluate model performance
python scripts/test.py
```

### 6. Real-time Recognition

```bash
# Start real-time gesture recognition
python scripts/realtime.py
```

## 🎮 Gesture Controls

| Gesture | Action | Description |
|---------|--------|-------------|
| Open Palm | Cursor Reference | Sets reference position for cursor movement |
| Pinch | Left Click | Thumb and index finger together |
| Thumb + Middle | Right Click | Thumb and middle finger together |
| Two Fingers Up | Scroll Up | Index and middle fingers pointing up |
| Two Fingers Down | Scroll Down | Index and middle fingers pointing down |
| Closed Fist | Drag Toggle | Start/stop mouse dragging |

## 🧠 Model Architecture

```
Input: (63, 1) - Hand landmark features
       ↓
Conv1D(64, kernel=3) + ReLU + MaxPool
       ↓
Conv1D(128, kernel=3) + ReLU + MaxPool  
       ↓
Conv1D(256, kernel=3) + ReLU + MaxPool
       ↓
Flatten + Dropout(0.3)
       ↓
Dense(512) + ReLU → Dense(256) + ReLU
       ↓
Dense(6) + Softmax
       ↓
Output: 6 gesture classes
```

## 📊 Performance Metrics

- **Target Accuracy**: ≥ 90%
- **Latency**: < 100ms for real-time processing
- **Distance Range**: 0.5-2.5 meters from camera
- **Lighting**: Robust under various lighting conditions

## 🔧 Configuration

### Model Parameters
- **Epochs**: 10 (with early stopping)
- **Batch Size**: 32
- **Learning Rate**: 0.001 (Adam optimizer)
- **Dropout**: 0.3 for regularization

### Real-time Settings
- **Confidence Threshold**: 0.7
- **Gesture Buffer**: 5 frames for smoothing
- **Cooldown**: 0.5 seconds between actions

## 📋 Requirements

```
tensorflow==2.13.0
keras==2.13.1
opencv-python==********
mediapipe==0.10.3
numpy==1.24.3
matplotlib==3.7.2
scikit-learn==1.3.0
pyautogui==0.9.54
pandas==2.0.3
seaborn==0.12.2
pillow==10.0.0
```

## 🔍 Usage Examples

### Training with Custom Data

```python
from scripts.train import GestureTrainer

trainer = GestureTrainer()
model, history, data = trainer.run_training_pipeline(
    epochs=15,
    batch_size=64,
    use_synthetic=False  # Use real data
)
```

### Real-time Recognition

```python
from scripts.realtime import RealTimeGestureRecognizer

recognizer = RealTimeGestureRecognizer()
recognizer.confidence_threshold = 0.8  # Adjust confidence
recognizer.mouse_enabled = True        # Enable mouse control
recognizer.run()
```

## 🧪 Testing and Validation

The system includes comprehensive testing:

1. **Unit Tests**: Individual component validation
2. **Integration Tests**: End-to-end pipeline testing
3. **Performance Tests**: Latency and accuracy benchmarks
4. **Robustness Tests**: Various lighting and distance conditions

## 🚨 Troubleshooting

### Common Issues

1. **Camera not detected**
   ```bash
   # Check camera permissions and availability
   python -c "import cv2; print(cv2.VideoCapture(0).isOpened())"
   ```

2. **Model not found**
   ```bash
   # Ensure training completed successfully
   ls models/gesture_cnn.h5
   ```

3. **Poor recognition accuracy**
   - Ensure good lighting conditions
   - Keep hand within 0.5-2.5m from camera
   - Avoid cluttered backgrounds
   - Calibrate confidence threshold

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/improvement`)
3. Commit changes (`git commit -am 'Add improvement'`)
4. Push to branch (`git push origin feature/improvement`)
5. Create Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- MediaPipe team for hand landmark detection
- TensorFlow/Keras for deep learning framework
- OpenCV community for computer vision tools
- PyAutoGUI for mouse control integration

## 📞 Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the documentation in `/docs`

## 🔄 Complete Setup Guide

### Step-by-Step Installation

1. **Environment Setup**
   ```bash
   # Create virtual environment (recommended)
   python -m venv gesture_env

   # Activate environment
   # Windows:
   gesture_env\Scripts\activate
   # Linux/Mac:
   source gesture_env/bin/activate

   # Install dependencies
   pip install -r requirements.txt
   ```

2. **Run Complete Pipeline**
   ```bash
   # 1. Prepare dataset
   python scripts/dataset_preparation.py

   # 2. Extract landmarks
   python scripts/preprocess.py

   # 3. Train model
   python scripts/train.py

   # 4. Evaluate performance
   python scripts/test.py

   # 5. Start real-time recognition
   python scripts/realtime.py
   ```

---

**Note**: This system is designed for educational and research purposes. Ensure proper permissions and safety measures when using mouse control features.
