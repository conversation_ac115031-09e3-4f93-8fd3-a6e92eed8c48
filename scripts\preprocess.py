"""
Hand Gesture Preprocessing Pipeline
Extracts MediaPipe hand landmarks and converts to feature vectors
"""

import cv2
import mediapipe as mp
import numpy as np
import pandas as pd
import os
from pathlib import Path
import pickle
from sklearn.preprocessing import LabelEncoder
import matplotlib.pyplot as plt

class HandLandmarkExtractor:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=True,
            max_num_hands=1,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
    def extract_landmarks(self, image):
        """
        Extract hand landmarks from image using MediaPipe
        Returns normalized 63-dimensional feature vector (21 landmarks * 3 coordinates)
        """
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Process the image
        results = self.hands.process(rgb_image)
        
        if results.multi_hand_landmarks:
            # Get the first hand landmarks
            hand_landmarks = results.multi_hand_landmarks[0]
            
            # Extract coordinates
            landmarks = []
            for landmark in hand_landmarks.landmark:
                landmarks.extend([landmark.x, landmark.y, landmark.z])
            
            return np.array(landmarks)
        else:
            # Return zeros if no hand detected
            return np.zeros(63)
    
    def normalize_landmarks(self, landmarks):
        """
        Normalize landmarks relative to wrist position and hand size
        """
        if np.all(landmarks == 0):
            return landmarks
            
        # Reshape to (21, 3) for easier manipulation
        landmarks_reshaped = landmarks.reshape(21, 3)
        
        # Get wrist position (landmark 0)
        wrist = landmarks_reshaped[0]
        
        # Translate all landmarks relative to wrist
        normalized = landmarks_reshaped - wrist
        
        # Calculate hand size (distance from wrist to middle finger tip)
        middle_finger_tip = landmarks_reshaped[12]
        hand_size = np.linalg.norm(middle_finger_tip - wrist)
        
        # Scale by hand size if it's not zero
        if hand_size > 0:
            normalized = normalized / hand_size
            
        return normalized.flatten()
    
    def process_dataset(self, dataset_path="dataset"):
        """
        Process entire dataset and extract landmarks
        """
        dataset_path = Path(dataset_path)
        processed_data = []
        labels = []
        
        gesture_classes = [
            "open_palm", "pinch", "thumb_middle_pinch", 
            "two_fingers_up", "two_fingers_down", "closed_fist"
        ]
        
        print("Processing dataset...")
        
        for split in ['train', 'val', 'test']:
            split_path = dataset_path / split
            if not split_path.exists():
                continue
                
            print(f"Processing {split} split...")
            
            for gesture_class in gesture_classes:
                gesture_path = split_path / gesture_class
                if not gesture_path.exists():
                    continue
                    
                image_files = list(gesture_path.glob('*.jpg')) + list(gesture_path.glob('*.png'))
                
                for img_file in image_files:
                    # Load image
                    image = cv2.imread(str(img_file))
                    if image is None:
                        continue
                        
                    # Extract landmarks
                    landmarks = self.extract_landmarks(image)
                    
                    # Normalize landmarks
                    normalized_landmarks = self.normalize_landmarks(landmarks)
                    
                    # Store data
                    processed_data.append({
                        'landmarks': normalized_landmarks,
                        'label': gesture_class,
                        'split': split,
                        'filename': img_file.name
                    })
                    
                print(f"Processed {len(image_files)} images for {gesture_class}")
        
        return processed_data
    
    def save_processed_data(self, processed_data, output_path="dataset/processed"):
        """
        Save processed landmark data to files
        """
        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True)
        
        # Separate data by split
        splits = {'train': [], 'val': [], 'test': []}
        
        for data in processed_data:
            splits[data['split']].append(data)
        
        # Save each split
        for split_name, split_data in splits.items():
            if not split_data:
                continue
                
            # Prepare features and labels
            X = np.array([item['landmarks'] for item in split_data])
            y = [item['label'] for item in split_data]
            filenames = [item['filename'] for item in split_data]
            
            # Save as numpy arrays
            np.save(output_path / f'X_{split_name}.npy', X)
            
            # Save labels and filenames as pickle
            with open(output_path / f'y_{split_name}.pkl', 'wb') as f:
                pickle.dump(y, f)
                
            with open(output_path / f'filenames_{split_name}.pkl', 'wb') as f:
                pickle.dump(filenames, f)
                
            print(f"Saved {split_name} data: {X.shape[0]} samples, {X.shape[1]} features")
        
        # Save label encoder
        all_labels = [item['label'] for item in processed_data]
        label_encoder = LabelEncoder()
        label_encoder.fit(all_labels)
        
        with open(output_path / 'label_encoder.pkl', 'wb') as f:
            pickle.dump(label_encoder, f)
            
        print(f"Label encoder saved. Classes: {label_encoder.classes_}")
        
    def visualize_landmarks(self, image, landmarks):
        """
        Visualize hand landmarks on image
        """
        if np.all(landmarks == 0):
            return image
            
        # Convert landmarks back to image coordinates
        landmarks_reshaped = landmarks.reshape(21, 3)
        h, w = image.shape[:2]
        
        # Draw landmarks
        for i, (x, y, z) in enumerate(landmarks_reshaped):
            # Convert normalized coordinates to pixel coordinates
            px, py = int(x * w), int(y * h)
            cv2.circle(image, (px, py), 5, (0, 255, 0), -1)
            cv2.putText(image, str(i), (px+5, py-5), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
            
        return image
    
    def load_processed_data(self, processed_path="dataset/processed"):
        """
        Load processed data from files
        """
        processed_path = Path(processed_path)
        
        data = {}
        for split in ['train', 'val', 'test']:
            X_file = processed_path / f'X_{split}.npy'
            y_file = processed_path / f'y_{split}.pkl'
            
            if X_file.exists() and y_file.exists():
                X = np.load(X_file)
                with open(y_file, 'rb') as f:
                    y = pickle.load(f)
                data[split] = (X, y)
                
        # Load label encoder
        le_file = processed_path / 'label_encoder.pkl'
        if le_file.exists():
            with open(le_file, 'rb') as f:
                label_encoder = pickle.load(f)
            data['label_encoder'] = label_encoder
            
        return data

def main():
    """
    Main preprocessing pipeline
    """
    print("Starting hand landmark extraction...")
    
    # Initialize extractor
    extractor = HandLandmarkExtractor()
    
    # Process dataset
    processed_data = extractor.process_dataset()
    
    if processed_data:
        # Save processed data
        extractor.save_processed_data(processed_data)
        
        print(f"\nProcessing completed!")
        print(f"Total samples processed: {len(processed_data)}")
        
        # Display class distribution
        class_counts = {}
        for item in processed_data:
            label = item['label']
            class_counts[label] = class_counts.get(label, 0) + 1
            
        print("\nClass distribution:")
        for label, count in class_counts.items():
            print(f"{label}: {count} samples")
    else:
        print("No data processed. Please ensure dataset images are available.")

if __name__ == "__main__":
    main()
