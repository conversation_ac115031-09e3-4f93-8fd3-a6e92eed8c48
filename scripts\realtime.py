"""
Real-time Hand Gesture Recognition System
Captures webcam feed, recognizes gestures, and controls mouse actions
"""

import cv2
import mediapipe as mp
import numpy as np
import pyautogui
import time
import pickle
from pathlib import Path
import threading
from collections import deque

# Import our custom model
from model import GestureCNN

class RealTimeGestureRecognizer:
    def __init__(self, model_path="models/gesture_cnn.h5", data_path="dataset/processed"):
        self.model_path = Path(model_path)
        self.data_path = Path(data_path)
        
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Gesture classes
        self.gesture_classes = [
            "open_palm", "pinch", "thumb_middle_pinch", 
            "two_fingers_up", "two_fingers_down", "closed_fist"
        ]
        
        # Mouse control settings
        self.mouse_enabled = True
        self.confidence_threshold = 0.7
        self.gesture_buffer = deque(maxlen=5)  # Smooth predictions
        
        # Gesture state tracking
        self.current_gesture = None
        self.last_gesture_time = time.time()
        self.gesture_cooldown = 0.5  # Seconds between actions
        
        # Mouse control variables
        self.dragging = False
        self.last_mouse_pos = None
        
        # Load model and label encoder
        self.model = None
        self.label_encoder = None
        self.load_model_and_encoder()
        
        # PyAutoGUI settings
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.01
        
    def load_model_and_encoder(self):
        """
        Load trained model and label encoder
        """
        try:
            # Load model
            if self.model_path.exists():
                self.model = GestureCNN()
                self.model.load_model(str(self.model_path))
                print("Model loaded successfully!")
            else:
                print(f"Model not found at {self.model_path}")
                return False
                
            # Load label encoder
            le_file = self.data_path / 'label_encoder.pkl'
            if le_file.exists():
                with open(le_file, 'rb') as f:
                    self.label_encoder = pickle.load(f)
                print("Label encoder loaded successfully!")
            else:
                print(f"Label encoder not found at {le_file}")
                return False
                
            return True
            
        except Exception as e:
            print(f"Error loading model or encoder: {e}")
            return False
    
    def extract_landmarks(self, image):
        """
        Extract hand landmarks from image
        """
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_image)
        
        if results.multi_hand_landmarks:
            hand_landmarks = results.multi_hand_landmarks[0]
            
            # Extract coordinates
            landmarks = []
            for landmark in hand_landmarks.landmark:
                landmarks.extend([landmark.x, landmark.y, landmark.z])
            
            return np.array(landmarks), hand_landmarks
        
        return None, None
    
    def normalize_landmarks(self, landmarks):
        """
        Normalize landmarks relative to wrist position and hand size
        """
        if landmarks is None:
            return None
            
        # Reshape to (21, 3)
        landmarks_reshaped = landmarks.reshape(21, 3)
        
        # Get wrist position (landmark 0)
        wrist = landmarks_reshaped[0]
        
        # Translate relative to wrist
        normalized = landmarks_reshaped - wrist
        
        # Calculate hand size
        middle_finger_tip = landmarks_reshaped[12]
        hand_size = np.linalg.norm(middle_finger_tip - wrist)
        
        # Scale by hand size
        if hand_size > 0:
            normalized = normalized / hand_size
            
        return normalized.flatten()
    
    def predict_gesture(self, landmarks):
        """
        Predict gesture from landmarks
        """
        if landmarks is None or self.model is None:
            return None, 0.0
            
        # Reshape for model input
        landmarks_input = landmarks.reshape(1, 63, 1)
        
        # Get prediction
        prediction = self.model.predict(landmarks_input, verbose=0)
        predicted_class = np.argmax(prediction)
        confidence = np.max(prediction)
        
        # Get gesture name
        gesture_name = self.label_encoder.classes_[predicted_class]
        
        return gesture_name, confidence
    
    def smooth_predictions(self, gesture, confidence):
        """
        Smooth predictions using a buffer
        """
        if confidence > self.confidence_threshold:
            self.gesture_buffer.append(gesture)
        
        if len(self.gesture_buffer) >= 3:
            # Get most common gesture in buffer
            gesture_counts = {}
            for g in self.gesture_buffer:
                gesture_counts[g] = gesture_counts.get(g, 0) + 1
            
            most_common = max(gesture_counts, key=gesture_counts.get)
            if gesture_counts[most_common] >= 2:
                return most_common
        
        return None
    
    def execute_mouse_action(self, gesture):
        """
        Execute mouse action based on recognized gesture
        """
        if not self.mouse_enabled:
            return
            
        current_time = time.time()
        
        # Check cooldown
        if current_time - self.last_gesture_time < self.gesture_cooldown:
            return
            
        try:
            if gesture == "open_palm":
                # Cursor movement - get current mouse position for reference
                self.last_mouse_pos = pyautogui.position()
                
            elif gesture == "pinch":
                # Left click
                pyautogui.click()
                print("Action: Left Click")
                self.last_gesture_time = current_time
                
            elif gesture == "thumb_middle_pinch":
                # Right click
                pyautogui.rightClick()
                print("Action: Right Click")
                self.last_gesture_time = current_time
                
            elif gesture == "two_fingers_up":
                # Scroll up
                pyautogui.scroll(3)
                print("Action: Scroll Up")
                self.last_gesture_time = current_time
                
            elif gesture == "two_fingers_down":
                # Scroll down
                pyautogui.scroll(-3)
                print("Action: Scroll Down")
                self.last_gesture_time = current_time
                
            elif gesture == "closed_fist":
                # Start/stop dragging
                if not self.dragging:
                    pyautogui.mouseDown()
                    self.dragging = True
                    print("Action: Start Drag")
                else:
                    pyautogui.mouseUp()
                    self.dragging = False
                    print("Action: Stop Drag")
                self.last_gesture_time = current_time
                
        except Exception as e:
            print(f"Error executing mouse action: {e}")
    
    def draw_landmarks_and_info(self, image, hand_landmarks, gesture, confidence):
        """
        Draw landmarks and gesture information on image
        """
        if hand_landmarks:
            # Draw hand landmarks
            self.mp_drawing.draw_landmarks(
                image, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
        
        # Draw gesture information
        height, width = image.shape[:2]
        
        # Background for text
        cv2.rectangle(image, (10, 10), (400, 120), (0, 0, 0), -1)
        
        # Gesture text
        if gesture:
            color = (0, 255, 0) if confidence > self.confidence_threshold else (0, 255, 255)
            cv2.putText(image, f"Gesture: {gesture}", (20, 40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            cv2.putText(image, f"Confidence: {confidence:.2f}", (20, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        else:
            cv2.putText(image, "No hand detected", (20, 40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Instructions
        cv2.putText(image, "Press 'q' to quit, 'm' to toggle mouse", (20, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Mouse status
        mouse_status = "ON" if self.mouse_enabled else "OFF"
        mouse_color = (0, 255, 0) if self.mouse_enabled else (0, 0, 255)
        cv2.putText(image, f"Mouse: {mouse_status}", (width - 150, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, mouse_color, 2)
        
        return image
    
    def run(self):
        """
        Run real-time gesture recognition
        """
        if self.model is None or self.label_encoder is None:
            print("Model or label encoder not loaded. Cannot start real-time recognition.")
            return
            
        print("Starting real-time gesture recognition...")
        print("Gesture Actions:")
        print("- Open Palm: Cursor reference")
        print("- Pinch: Left click")
        print("- Thumb+Middle: Right click")
        print("- Two Fingers Up: Scroll up")
        print("- Two Fingers Down: Scroll down")
        print("- Closed Fist: Drag toggle")
        print("\nPress 'q' to quit, 'm' to toggle mouse control")
        
        # Initialize webcam
        cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        if not cap.isOpened():
            print("Error: Could not open webcam")
            return
        
        fps_counter = 0
        start_time = time.time()
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Flip frame horizontally for mirror effect
                frame = cv2.flip(frame, 1)
                
                # Extract landmarks
                landmarks, hand_landmarks = self.extract_landmarks(frame)
                
                gesture = None
                confidence = 0.0
                
                if landmarks is not None:
                    # Normalize landmarks
                    normalized_landmarks = self.normalize_landmarks(landmarks)
                    
                    if normalized_landmarks is not None:
                        # Predict gesture
                        gesture, confidence = self.predict_gesture(normalized_landmarks)
                        
                        # Smooth predictions
                        smooth_gesture = self.smooth_predictions(gesture, confidence)
                        
                        if smooth_gesture and smooth_gesture != self.current_gesture:
                            self.current_gesture = smooth_gesture
                            self.execute_mouse_action(smooth_gesture)
                
                # Draw landmarks and information
                frame = self.draw_landmarks_and_info(frame, hand_landmarks, gesture, confidence)
                
                # Calculate and display FPS
                fps_counter += 1
                if fps_counter % 30 == 0:
                    elapsed_time = time.time() - start_time
                    fps = fps_counter / elapsed_time
                    cv2.putText(frame, f"FPS: {fps:.1f}", (10, frame.shape[0] - 20), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # Display frame
                cv2.imshow('Hand Gesture Recognition', frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('m'):
                    self.mouse_enabled = not self.mouse_enabled
                    print(f"Mouse control: {'ON' if self.mouse_enabled else 'OFF'}")
                
        except KeyboardInterrupt:
            print("\nStopping gesture recognition...")
        
        finally:
            # Cleanup
            if self.dragging:
                pyautogui.mouseUp()
            cap.release()
            cv2.destroyAllWindows()
            print("Real-time gesture recognition stopped.")

def main():
    """
    Main function to run real-time gesture recognition
    """
    recognizer = RealTimeGestureRecognizer()
    recognizer.run()

if __name__ == "__main__":
    main()
